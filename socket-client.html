<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO Client Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message-box {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            height: 200px;
            overflow-y: auto;
            background-color: #f9f9f9;
        }
        input, button {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Socket.IO Client Demo</h1>
        
        <div id="status" class="status disconnected">
            Chưa kết nối
        </div>
        
        <div>
            <input type="text" id="serverUrl" placeholder="Socket server URL" value="http://localhost:3000">
            <button onclick="connectSocket()">Kết nối</button>
            <button onclick="disconnectSocket()">Ngắt kết nối</button>
        </div>
        
        <div>
            <h3>Gửi Message</h3>
            <input type="text" id="messageInput" placeholder="Nhập message...">
            <button onclick="sendMessage()">Gửi</button>
        </div>
        
        <div>
            <h3>Join/Leave Room</h3>
            <input type="text" id="roomInput" placeholder="Tên room...">
            <button onclick="joinRoom()">Join Room</button>
            <button onclick="leaveRoom()">Leave Room</button>
        </div>
        
        <div>
            <h3>Messages</h3>
            <div id="messages" class="message-box"></div>
            <button onclick="clearMessages()">Xóa messages</button>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function updateStatus(message, isConnected) {
            statusDiv.textContent = message;
            statusDiv.className = `status ${isConnected ? 'connected' : 'disconnected'}`;
        }
        
        function addMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            messagesDiv.appendChild(messageElement);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function connectSocket() {
            const url = document.getElementById('serverUrl').value;
            
            if (socket) {
                socket.disconnect();
            }
            
            socket = io(url);
            
            socket.on('connect', () => {
                updateStatus(`Đã kết nối (ID: ${socket.id})`, true);
                addMessage('✅ Kết nối thành công!');
            });
            
            socket.on('disconnect', () => {
                updateStatus('Đã ngắt kết nối', false);
                addMessage('❌ Đã ngắt kết nối');
            });
            
            socket.on('connect_error', (error) => {
                updateStatus('Lỗi kết nối', false);
                addMessage(`🚫 Lỗi kết nối: ${error.message}`);
            });
            
            // Lắng nghe các event từ server
            socket.on('message', (data) => {
                addMessage(`📨 Message: ${JSON.stringify(data)}`);
            });
            
            socket.on('notification', (data) => {
                addMessage(`🔔 Notification: ${JSON.stringify(data)}`);
            });
            
            socket.on('room_joined', (data) => {
                addMessage(`🏠 Đã join room: ${JSON.stringify(data)}`);
            });
            
            socket.on('room_left', (data) => {
                addMessage(`🚪 Đã leave room: ${JSON.stringify(data)}`);
            });
        }
        
        function disconnectSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
        }
        
        function sendMessage() {
            const message = document.getElementById('messageInput').value;
            if (socket && socket.connected && message) {
                socket.emit('message', { text: message, timestamp: new Date().toISOString() });
                addMessage(`📤 Đã gửi: ${message}`);
                document.getElementById('messageInput').value = '';
            } else {
                addMessage('❌ Không thể gửi message (chưa kết nối hoặc message trống)');
            }
        }
        
        function joinRoom() {
            const room = document.getElementById('roomInput').value;
            if (socket && socket.connected && room) {
                socket.emit('join_room', { room: room });
                addMessage(`🏠 Đang join room: ${room}`);
            } else {
                addMessage('❌ Không thể join room (chưa kết nối hoặc tên room trống)');
            }
        }
        
        function leaveRoom() {
            const room = document.getElementById('roomInput').value;
            if (socket && socket.connected && room) {
                socket.emit('leave_room', { room: room });
                addMessage(`🚪 Đang leave room: ${room}`);
            } else {
                addMessage('❌ Không thể leave room (chưa kết nối hoặc tên room trống)');
            }
        }
        
        function clearMessages() {
            messagesDiv.innerHTML = '';
        }
        
        // Enter key để gửi message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Enter key để join room
        document.getElementById('roomInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                joinRoom();
            }
        });
    </script>
</body>
</html>
