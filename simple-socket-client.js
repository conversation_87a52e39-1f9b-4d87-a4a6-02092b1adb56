const io = require('socket.io-client');

// Kết nối đơn giản tới socket server
const socket = io('http://localhost:3000'); // Thay đổi URL của bạn

// Khi kết nối thành công
socket.on('connect', () => {
    console.log('Kết nối thành công! Socket ID:', socket.id);
    
    // Gửi một message test
    socket.emit('test', { message: 'Hello Server!' });
});

// Lắng nghe response từ server
socket.on('response', (data) => {
    console.log('Server response:', data);
});

// Xử lý lỗi kết nối
socket.on('connect_error', (error) => {
    console.log('Lỗi kết nối:', error.message);
});

// Khi mất kết nối
socket.on('disconnect', () => {
    console.log('Đã ngắt kết nối');
});

console.log('Đang kết nối tới socket server...');
