const io = require('socket.io-client');

// Cấu hình kết nối socket
const SOCKET_URL = 'http://localhost:3000'; // Thay đổi URL server của bạn
const socket = io(SOCKET_URL, {
    // <PERSON><PERSON>c tùy chọn kết nối
    autoConnect: true,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionAttempts: 5,
    timeout: 20000,
    forceNew: false
});

// Event listeners cho socket connection
socket.on('connect', () => {
    console.log('✅ Kết nối socket thành công!');
    console.log('Socket ID:', socket.id);
    
    // Gửi message đầu tiên sau khi kết nối
    socket.emit('hello', { message: 'Xin chào từ client!' });
});

socket.on('disconnect', (reason) => {
    console.log('❌ Mất kết nối socket:', reason);
});

socket.on('connect_error', (error) => {
    console.log('🚫 Lỗi kết nối:', error.message);
});

socket.on('reconnect', (attemptNumber) => {
    console.log('🔄 Kết nối lại thành công sau', attemptNumber, 'lần thử');
});

socket.on('reconnect_error', (error) => {
    console.log('🔄❌ Lỗi khi thử kết nối lại:', error.message);
});

socket.on('reconnect_failed', () => {
    console.log('🔄❌ Không thể kết nối lại sau nhiều lần thử');
});

// Lắng nghe các event từ server
socket.on('message', (data) => {
    console.log('📨 Nhận message từ server:', data);
});

socket.on('notification', (data) => {
    console.log('🔔 Thông báo:', data);
});

socket.on('user_joined', (data) => {
    console.log('👤 User mới tham gia:', data);
});

socket.on('user_left', (data) => {
    console.log('👤 User rời khỏi:', data);
});

// Các hàm tiện ích để gửi data
function sendMessage(message) {
    if (socket.connected) {
        socket.emit('message', { 
            text: message, 
            timestamp: new Date().toISOString(),
            userId: socket.id 
        });
        console.log('📤 Đã gửi message:', message);
    } else {
        console.log('❌ Socket chưa kết nối, không thể gửi message');
    }
}

function joinRoom(roomName) {
    if (socket.connected) {
        socket.emit('join_room', { room: roomName });
        console.log('🏠 Tham gia room:', roomName);
    } else {
        console.log('❌ Socket chưa kết nối, không thể join room');
    }
}

function leaveRoom(roomName) {
    if (socket.connected) {
        socket.emit('leave_room', { room: roomName });
        console.log('🚪 Rời khỏi room:', roomName);
    } else {
        console.log('❌ Socket chưa kết nối, không thể leave room');
    }
}

function sendToRoom(roomName, message) {
    if (socket.connected) {
        socket.emit('room_message', { 
            room: roomName, 
            message: message,
            timestamp: new Date().toISOString()
        });
        console.log('🏠📤 Gửi message tới room', roomName + ':', message);
    } else {
        console.log('❌ Socket chưa kết nối, không thể gửi message tới room');
    }
}

// Xử lý khi tắt ứng dụng
process.on('SIGINT', () => {
    console.log('\n🔌 Đang ngắt kết nối socket...');
    socket.disconnect();
    process.exit(0);
});

// Export socket và các hàm để sử dụng ở file khác
module.exports = {
    socket,
    sendMessage,
    joinRoom,
    leaveRoom,
    sendToRoom
};

// Demo sử dụng (uncomment để test)
/*
setTimeout(() => {
    sendMessage('Hello from client!');
    joinRoom('general');
    
    setTimeout(() => {
        sendToRoom('general', 'Hello everyone in general room!');
    }, 2000);
}, 3000);
*/

console.log('🚀 Socket client đã khởi tạo, đang kết nối tới:', SOCKET_URL);
